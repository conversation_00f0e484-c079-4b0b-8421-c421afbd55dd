[project]
name = "pypix"
version = "1.0.4"
description = "Biblioteca python para gerar br-code e qr-code do PIX."
authors = [
    { name = "cleiton", email = "<EMAIL>"}
]
license = { text = "MIT" }
readme = "README.md"
requires-python = ">=3.12,<4.0"

dependencies = [
    "qrcode (>=7.4.2,<8.0.0)",
    "cairosvg (>=2.7.1,<3.0.0)",
]

packages = [
    { include = "pypix" }
]

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
