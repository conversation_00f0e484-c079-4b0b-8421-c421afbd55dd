.\" Manpage for qr
.TH QR 1 "6 Feb 2023" "7.4.2" "Python QR tool"
.SH NAME
qr \- script to create QR codes at the command line
.SH SYNOPSIS
qr [\-\-help] [\-\-factory=FACTORY] [\-\-optimize=OPTIMIZE] [\-\-error\-correction=LEVEL] [data]
.SH DESCRIPTION
This script uses the python qrcode module. It can take data from stdin or from the commandline and generate a QR code.
Normally it will output the QR code as ascii art to the terminal. If the output is piped to a file, it will output the image (default type of PNG).
.SH OPTIONS
.PP
\fB\ \-h, \-\-help\fR
.RS 4
Show a help message.
.RE

.PP
\fB\ \-\-factory=FACTORY\fR
.RS 4
Full python path to the image factory class to create the
image with. You can use the following shortcuts to the
built-in image factory classes: pil (default), png (
default if pillow is not installed), svg, svg-fragment,
svg-path.
.RE

.PP
\fB\ \-\-optimize=OPTIMIZE\fR
.RS 4
Optimize the data by looking for chunks of at least this
many characters that could use a more efficient encoding
method. Use 0 to turn off chunk optimization.
.RE

.PP
\fB\ \-\-error\-correction=LEVEL\fR
.RS 4
The error correction level to use. Choices are L (7%),
M (15%, default), Q (25%), and H (30%).
.RE

.PP
\fB\ data\fR
.RS 4
The data from which the QR code will be generated.
.RE

.SH SEE ALSO
https://github.com/lincolnloop/python-qrcode/

