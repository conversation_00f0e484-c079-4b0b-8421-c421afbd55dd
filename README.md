# pypix

<img src="https://github.com/cleitonleonel/pypix/blob/master/pypix.png?raw=true" alt="pypix" width="450"/>

**pypix** é uma biblioteca Python baseada no projeto [GPIX](https://github.com/hiagodotme/gpix.git) de Hiago Silva Souza.  
Ela facilita a geração de códigos BR-Code estáticos e dinâmicos para transações via PIX, além de permitir personalização avançada de QR Codes com estilos visuais.

---

## 🛠️ Instalação

### Requisitos do Sistema

- **Python**: 3.12+ (gerenciado automaticamente pelo uv)
- **Sistema Operacional**: macOS, Linux, Windows
- **Cairo**: Biblioteca gráfica para renderização de QR Codes estilizados

### Pré-requisitos

**macOS:**
```bash
brew install cairo
```

**Ubuntu/Debian:**
```bash
sudo apt-get install libcairo2-dev
```

**Windows:**
```bash
# Instalar via conda ou usar WSL
conda install cairo
```

### Instalação do projeto

Clone o repositório e instale as dependências usando o `uv`:

```bash
git clone https://github.com/cleitonleonel/pypix.git
cd pypix
# Instalar uv (se não tiver)
curl -LsSf https://astral.sh/uv/install.sh | sh
# Sincronizar dependências com Python 3.12
uv sync --python 3.12
```

---

## ▶️ Como executar

### Executar o exemplo principal

```bash
# No macOS, definir variável de ambiente para Cairo
export DYLD_LIBRARY_PATH="/opt/homebrew/opt/cairo/lib:$DYLD_LIBRARY_PATH"

# Executar o projeto
uv run python main.py
```

### Executar com diferentes configurações

```bash
# Executar em modo interativo
uv run python -i main.py

# Executar testes
uv run python -m pytest tests/
```

---

## 🚀 Como usar

```python
from pypix.pix import Pix
from pypix.core.utils.pix_parser import parse_br_code
from pypix.core.styles.qr_styler import GradientMode
from pypix.core.styles.marker_styles import MarkerStyle
from pypix.core.styles.border_styles import BorderStyle
from pypix.core.styles.line_styles import LineStyle
from pypix.core.styles.frame_styles import FrameStyle
import logging
import json

# Configuração básica do logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def normal_static():
    """PIX Estático com valor fixo (Testado com Nubank, Inter, Caixa, MercadoPago)"""
    pix.set_name_receiver('Cleiton Leonel Creton')
    pix.set_city_receiver('Cariacica')
    pix.set_key('b5fe1edc-d108-410f-b966-eccaaca75e4f')
    pix.set_identification('123')
    pix.set_zipcode_receiver('********')
    pix.set_description('Doação com valor fixo - PYPIX')
    pix.set_amount(5.0)

    print('\nDoação com valor definido - PYPIX >>>>\n', pix.get_br_code())


def simple_static():
    """PIX Estático com valor livre (Inter exige valor mínimo de R$ 1, Nubank/Caixa aceitam qualquer valor)"""
    pix.set_name_receiver('Cleiton Leonel Creton')
    pix.set_city_receiver('Cariacica')
    pix.set_key('b5fe1edc-d108-410f-b966-eccaaca75e4f')
    pix.set_description('Doação Livre / QRCODE - PYPIX')

    print('Doação sem valor definido - PYPIX >>>>\n', pix.get_br_code())


def dynamic():
    """PIX Dinâmico - requer URL de payload (não testado)"""
    pix.set_name_receiver('MasterSystem LTDA')
    pix.set_city_receiver('Cariacica')
    pix.set_default_url_pix('url-location-psp')
    pix.is_single_transaction(True)

    print('\nBR-Code dinâmico - PYPIX >>>>\n', pix.get_br_code())


if __name__ == '__main__':
    pix = Pix()

    normal_static()
    # simple_static()
    # dynamic()

    # Converte o BR-Code em dicionário (útil para debugging)
    br_code = pix.get_br_code()
    decoded_data = parse_br_code(br_code)
    print(json.dumps(decoded_data, indent=4, ensure_ascii=False))

    # Gera e salva QR Code estilizado com ou sem logo
    base64qr = pix.save_qrcode(
        data=br_code,
        output='qrcode.png',
        box_size=7,
        border=1,
        custom_logo="pix.png",  # Pode ser PNG ou GIF
        marker_style=MarkerStyle.QUARTER_CIRCLE,
        border_style=BorderStyle.ROUNDED,
        line_style=LineStyle.ROUNDED,
        gradient_color="purple",
        gradient_mode=GradientMode.NORMAL,
        frame_style=FrameStyle.SCAN_ME_PURPLE,
        style_mode="Full"
    )

    if base64qr:
        logger.info("QR Code salvo com sucesso!")
        # print(base64qr)  # Base64 do QR Code (caso necessário)
        # pix.qr_ascii()  # Imprime QR Code no terminal
    else:
        logger.error("Erro ao salvar o QR Code.")
```

### Para executar este exemplo:

```bash
# Definir variável de ambiente (macOS)
export DYLD_LIBRARY_PATH="/opt/homebrew/opt/cairo/lib:$DYLD_LIBRARY_PATH"

# Executar com uv
uv run python main.py
```

---

## 🔧 Troubleshooting

### Erro: "no library called cairo-2 was found"

**Solução para macOS:**
```bash
# Instalar Cairo
brew install cairo

# Definir variável de ambiente
export DYLD_LIBRARY_PATH="/opt/homebrew/opt/cairo/lib:$DYLD_LIBRARY_PATH"
```

**Solução para Linux:**
```bash
# Ubuntu/Debian
sudo apt-get install libcairo2-dev

# CentOS/RHEL
sudo yum install cairo-devel
```

### Erro: "Python version not supported"

```bash
# Verificar versão do Python
uv run python --version

# Reinstalar com Python 3.12
uv sync --python 3.12
```

### QR Code não está sendo gerado

Verifique se:
1. A biblioteca Cairo está instalada
2. As variáveis de ambiente estão definidas
3. O arquivo de logo existe (se especificado)

---

## 🙌 Essa lib foi útil pra você?

Se sim, considere fazer uma doação — pode ser até R$ 0,50 😄  
Para isso, é só escanear o QR Code abaixo, gerado com o próprio pypix:

<img src="https://github.com/cleitonleonel/pypix/blob/master/qrcode.png?raw=true" alt="QRCode Doação" width="250"/>

<img src="https://github.com/cleitonleonel/pypix/blob/master/artistic.gif?raw=true" alt="QRCode Doação" width="250"/>

---

## 👨‍💻 Autor

**Cleiton Leonel Creton**  
📧 <EMAIL>  
🔗 [GitHub](https://github.com/cleitonleonel)  
🔗 [LinkedIn](https://www.linkedin.com/in/cleiton-leonel-creton-331138167/)